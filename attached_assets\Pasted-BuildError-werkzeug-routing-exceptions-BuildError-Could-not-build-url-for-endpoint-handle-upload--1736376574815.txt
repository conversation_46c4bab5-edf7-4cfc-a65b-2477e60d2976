BuildError
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'handle_upload'. Did you mean 'upload' instead?

Traceback (most recent call last)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1536, in __call__
return self.wsgi_app(environ, start_response)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1514, in wsgi_app
response = self.handle_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1511, in wsgi_app
response = self.full_dispatch_request()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 919, in full_dispatch_request
rv = self.handle_user_exception(e)
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 917, in full_dispatch_request
rv = self.dispatch_request()
     ^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 902, in dispatch_request
return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask_login/utils.py", line 290, in decorated_view
return current_app.ensure_sync(func)(*args, **kwargs)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/app.py", line 104, in upload
return render_template("upload.html")
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/templating.py", line 150, in render_template
return _render(app, template, context)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/templating.py", line 131, in _render
rv = template.render(context)
     ^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/jinja2/environment.py", line 1295, in render
self.environment.handle_exception()
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/jinja2/environment.py", line 942, in handle_exception
raise rewrite_traceback_stack(source=source)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/templates/upload.html", line 1, in top-level template code
{% extends "base.html" %}
File "/home/<USER>/workspace/templates/base.html", line 51, in top-level template code
{% block content %}{% endblock %}
File "/home/<USER>/workspace/templates/upload.html", line 9, in block 'content'
<form method="POST" action="{{ url_for('handle_upload') }}" enctype="multipart/form-data" id="uploadForm">
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1121, in url_for
return self.handle_url_build_error(error, endpoint, values)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1110, in url_for
rv = url_adapter.build(  # type: ignore[union-attr]
     
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/werkzeug/routing/map.py", line 924, in build
raise BuildError(endpoint, values, method, self)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'handle_upload'. Did you mean 'upload' instead?
The debugger caught an exception in your WSGI application. You can now look at the traceback which led to the error.
To switch between the interactive traceback and the plaintext one, you can click on the "Traceback" headline. From the text traceback you can also create a paste of it.

Brought to you by DON'T PANIC, your friendly Werkzeug powered traceback interpreter