{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Login</h2>
                <form method="POST" action="{{ url_for('login', next=request.args.get('next', '')) }}" id="loginForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const form = this;
    const formData = new FormData(form);

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' //Important for CSRF protection
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                // Handle errors appropriately, e.g., display error messages to the user
                console.error('Error:', errorData);
                alert('Login failed. Please check your credentials.'); // Example error handling
                return null;
            });
        } else if (response.redirected) {
            window.location.href = response.url;
        } else {
            return response.json(); // Handle successful login response if needed
        }
    })
    .then(data => {
        if (data) {
            // Process successful login response if needed.
            console.log('Login successful:', data);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An unexpected error occurred during login.'); //Example error handling
    });
});
</script>
{% endblock %}