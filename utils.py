import os
import tarfile
import tempfile
import shutil
from datetime import datetime
from flask import current_app
from models import db, VoicemailMessage
import logging
import subprocess

def allowed_file(filename):
    return filename.endswith('.tgz')

def find_voicemail_file(ms_num, base_dir):
    """Search for voicemail file in flattened directory structure"""
    current_app.logger.info(f"Searching for voicemail file with ms_num: {ms_num}")
    for root, _, files in os.walk(base_dir):
        for file in files:
            if str(ms_num) in file and not file.endswith('.txt'):
                current_app.logger.info(f"Found voicemail file: {os.path.join(root, file)}")
                return os.path.join(root, file)
    current_app.logger.warning(f"No voicemail file found for ms_num: {ms_num}")
    return None

def safe_extract_member(tar, member, target_dir):
    """Safely extract a single member from tar archive"""
    try:
        # Override permissions and extract to a flattened structure
        member.mode = 0o644
        filename = os.path.basename(member.name)
        target_path = os.path.join(target_dir, filename)

        with tar.extractfile(member) as source, open(target_path, 'wb') as target:
            shutil.copyfileobj(source, target)
        return True
    except Exception as e:
        current_app.logger.warning(f"Failed to extract {member.name}: {str(e)}")
        return False

def convert_raw_to_mp3(input_path):
    """
    Convert raw audio file (U-Law, mono, 8000Hz) to MP3 format.
    Returns the path to the converted MP3 file.
    """
    try:
        # Create a temporary file for the MP3 output
        fd, output_path = tempfile.mkstemp(suffix='.mp3')
        os.close(fd)  # Close file descriptor as we only need the path

        # FFmpeg command to convert raw audio to MP3
        # -f mulaw : input format is mu-law
        # -ar 8000 : sample rate is 8000Hz
        # -ac 1 : mono audio (1 channel)
        # -acodec libmp3lame : use MP3 encoder
        cmd = [
            'ffmpeg', '-y',  # -y to overwrite output file
            '-f', 'mulaw',  # U-law input format
            '-ar', '8000',  # 8000Hz sample rate
            '-ac', '1',     # Mono
            '-i', input_path,  # Input file
            '-acodec', 'libmp3lame',  # MP3 encoder
            '-ar', '44100',  # Output sample rate
            '-ab', '128k',   # Bitrate
            output_path      # Output file
        ]

        # Run FFmpeg command
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise ValueError(f"FFmpeg conversion failed: {result.stderr}")

        return output_path
    except Exception as e:
        current_app.logger.error(f"Error converting audio file: {str(e)}")
        if 'output_path' in locals() and os.path.exists(output_path):
            os.remove(output_path)
        raise

def process_archive(file, upload_folder, archive_id=None):
    current_app.logger.info(f"Starting to process archive: {file.filename if hasattr(file, 'filename') else 'reprocessing existing archive'}")

    # Create temporary directories with unique names
    temp_dir = tempfile.mkdtemp(prefix='archive_', dir='/tmp')
    extract_dir = os.path.join(temp_dir, 'extracted')
    os.makedirs(extract_dir, exist_ok=True)

    current_app.logger.debug(f"Created temporary directory: {temp_dir}")

    try:
        # Save or copy the archive file
        if hasattr(file, 'save'):
            tgz_path = os.path.join(temp_dir, 'archive.tgz')
            file.save(tgz_path)
            current_app.logger.info(f"Saved uploaded file to: {tgz_path}")
        else:
            tgz_path = os.path.join(temp_dir, 'archive.tgz')
            shutil.copyfile(file.name, tgz_path)
            current_app.logger.info(f"Copied existing archive to: {tgz_path}")

        # Check file size
        file_size = os.path.getsize(tgz_path)
        current_app.logger.info(f"Archive file size: {file_size} bytes")
        if file_size < 100:
            raise ValueError(f"File appears to be invalid (size: {file_size} bytes)")

        # Process outer archive
        current_app.logger.info("Verifying archive integrity")
        npm_backup_tar = None

        try:
            with tarfile.open(tgz_path, 'r:gz') as tgz:
                # Get all members first
                members = tgz.getmembers()
                current_app.logger.info(f"Archive contains {len(members)} files")

                # Find npm backup tar file
                for member in members:
                    if 'npmBackup' in member.name and member.name.endswith('.tar'):
                        npm_backup_tar = member
                        break

                if not npm_backup_tar:
                    raise ValueError("No npm backup tar file found in archive")

                # Extract only the npm backup tar file
                current_app.logger.info(f"Found npm backup file: {npm_backup_tar.name}")
                extracted_tar = os.path.join(temp_dir, 'backup.tar')
                with tgz.extractfile(npm_backup_tar) as source, open(extracted_tar, 'wb') as target:
                    shutil.copyfileobj(source, target)

        except Exception as e:
            current_app.logger.error(f"Failed to process outer archive: {str(e)}")
            raise ValueError(f"Failed to process archive: {str(e)}")

        # Process inner tar
        try:
            with tarfile.open(extracted_tar, 'r') as tar:
                members = tar.getmembers()
                current_app.logger.info(f"Inner archive contains {len(members)} files")

                # Extract only postgresql.txt and voicemail files
                postgresql_found = False
                for member in members:
                    if ('postgresql.txt' in member.name or
                        '/speech_backup' in member.name):
                        if 'postgresql.txt' in member.name:
                            postgresql_found = True
                        if safe_extract_member(tar, member, extract_dir):
                            current_app.logger.debug(f"Successfully extracted: {member.name}")

                if not postgresql_found:
                    raise ValueError("Required postgresql.txt file not found in archive")

        except Exception as e:
            current_app.logger.error(f"Failed to process inner archive: {str(e)}")
            raise ValueError(f"Failed to process backup file: {str(e)}")

        # Find and process postgresql.txt
        postgresql_file = None
        for file in os.listdir(extract_dir):
            if file == 'postgresql.txt':
                postgresql_file = os.path.join(extract_dir, file)
                break

        if not postgresql_file:
            raise ValueError("Could not locate postgresql.txt after extraction")

        # Read and parse postgresql data
        try:
            with open(postgresql_file, 'r') as f:
                content = f.read()
                if not content.strip():
                    raise ValueError("postgresql.txt is empty")
                current_app.logger.info(f"Successfully read postgresql.txt ({len(content)} bytes)")
        except Exception as e:
            current_app.logger.error(f"Error reading postgresql.txt: {str(e)}")
            raise ValueError(f"Failed to read postgresql.txt: {str(e)}")

        # Parse and process messages
        messages = parse_postgresql_data(content)
        if not messages:
            raise ValueError("No voicemail messages found in postgresql.txt")

        current_app.logger.info(f"Processing {len(messages)} voicemail messages")
        processed_count = 0

        # Process each message
        for msg_data in messages:
            msg = VoicemailMessage(**msg_data)
            if archive_id:
                msg.archive_id = archive_id

            if msg.ms_num:
                voicemail_file = find_voicemail_file(msg.ms_num, extract_dir)
                if voicemail_file:
                    os.makedirs(upload_folder, exist_ok=True)
                    # Store raw file directly without any extension
                    dest_path = os.path.join(upload_folder, f"voicemail_{msg.ms_num}")
                    shutil.copy2(voicemail_file, dest_path)
                    msg.file_path = dest_path
                    current_app.logger.debug(f"Copied raw voicemail file for ms_num {msg.ms_num}")

            db.session.add(msg)
            processed_count += 1
            if processed_count % 100 == 0:
                current_app.logger.info(f"Processed {processed_count}/{len(messages)} messages")
                db.session.commit()

        db.session.commit()
        current_app.logger.info(f"Successfully processed {len(messages)} voicemail messages")
        return messages

    except Exception as e:
        current_app.logger.error(f"Error in process_archive: {str(e)}", exc_info=True)
        db.session.rollback()
        raise
    finally:
        # Clean up temporary extraction directory
        cleanup_temp_files(temp_dir)

def cleanup_temp_files(temp_dir):
    try:
        if os.path.exists(temp_dir):
            current_app.logger.info(f"Cleaning up temporary directory: {temp_dir}")
            shutil.rmtree(temp_dir)
            current_app.logger.info("Temporary directory cleaned up successfully")
    except Exception as e:
        current_app.logger.error(f"Error cleaning up temporary files: {e}")

def parse_postgresql_data(content):
    current_app.logger.info("Starting to parse PostgreSQL data")
    messages = []
    in_section = False
    line_count = 0

    for line in content.split('\n'):
        if '-- Data for Name: np_mbox_message;' in line:
            current_app.logger.info("Found voicemail messages section")
            in_section = True
            continue
        if in_section:
            if line.strip() == '\\.':
                break
            if line.strip():
                fields = line.strip().split('\t')
                if len(fields) >= 22:
                    line_count += 1
                    try:
                        messages.append({
                            'mbox_id': int(fields[0]) if fields[0] != 'NULL' else None,
                            'message_id': int(fields[1]) if fields[1] != 'NULL' else None,
                            'ms_flags': fields[2],
                            'ms_num': int(fields[3]) if fields[3] != 'NULL' else None,
                            'ms_frames': int(fields[4]) if fields[4] != 'NULL' else None,
                            'ms_num2': int(fields[5]) if fields[5] != 'NULL' else None,
                            'ms_frames2': int(fields[6]) if fields[6] != 'NULL' else None,
                            'ms_datetime': datetime.fromisoformat(fields[7].strip("'")) if fields[7] != 'NULL' else None,
                            'ms_datetime2': datetime.fromisoformat(fields[8].strip("'")) if fields[8] != 'NULL' else None,
                            'ms_node': fields[9],
                            'ms_net_cpy_lst_idx': int(fields[10]) if fields[10] != 'NULL' else None,
                            'ms_line_index': int(fields[11]) if fields[11] != 'NULL' else None,
                            'ms_next': int(fields[12]) if fields[12] != 'NULL' else None,
                            'ms_prev': int(fields[13]) if fields[13] != 'NULL' else None,
                            'ms_snext': int(fields[14]) if fields[14] != 'NULL' else None,
                            'ms_sprev': int(fields[15]) if fields[15] != 'NULL' else None,
                            'ms_sender': fields[16],
                            'ms_advum_replicated': fields[17] == 't',
                            'amis_phone': fields[18],
                            'amis_mbox': fields[19],
                            'ms_name': fields[20],
                            'ms_privacy': fields[21] == 't'
                        })
                        if line_count % 100 == 0:
                            current_app.logger.info(f"Processed {line_count} messages")
                    except Exception as e:
                        current_app.logger.error(f"Error parsing line {line_count}: {str(e)}")
                        current_app.logger.error(f"Line content: {line}")

    current_app.logger.info(f"Completed parsing. Found {len(messages)} messages")
    return messages