{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Upload Voicemail Archive</h2>
                {% if duplicate_file %}
                <div class="alert alert-warning">
                    <h4 class="alert-heading">Duplicate File Detected</h4>
                    <p>An archive with the name "{{ duplicate_file }}" already exists. How would you like to proceed?</p>
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="file" value="{{ duplicate_file }}">
                        <div class="btn-group" role="group">
                            <button type="submit" name="handle_duplicate" value="overwrite" class="btn btn-warning">Overwrite Existing</button>
                            <button type="submit" name="handle_duplicate" value="rename" class="btn btn-primary">Save as New Version</button>
                            <a href="{{ url_for('upload') }}" class="btn btn-secondary">Cancel Upload</a>
                        </div>
                    </form>
                </div>
                {% else %}
                <form method="POST" action="{{ url_for('upload') }}" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select .tgz archive file</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".tgz" required>
                    </div>
                    <div class="mb-3 d-none" id="progressContainer">
                        <label class="form-label">Upload Progress</label>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%" 
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <span class="d-flex align-items-center justify-content-center">
                                <span class="me-2">Upload and Process</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status" id="loadingSpinner"></span>
                            </span>
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
  const uploadForm = document.getElementById('uploadForm');
  const submitBtn = document.getElementById('submitBtn');
  const loadingSpinner = document.getElementById('loadingSpinner');
  const progressContainer = document.getElementById('progressContainer');
  const progressBar = progressContainer.querySelector('.progress-bar');

  // Progress smoothing settings
  const progressHistory = [];
  const historySize = 5;
  const minProgressIncrement = 0.5;
  let lastProgress = 0;
  let updateTimeout = null;

  function updateProgressBar(percent) {
    // Add to history and maintain size
    progressHistory.push(percent);
    if (progressHistory.length > historySize) {
      progressHistory.shift();
    }

    // Calculate smoothed progress
    const smoothedProgress = progressHistory.reduce((a, b) => a + b, 0) / progressHistory.length;

    // Only update if the change is significant
    if (smoothedProgress - lastProgress >= minProgressIncrement) {
      progressBar.style.width = `${smoothedProgress}%`;
      progressBar.setAttribute('aria-valuenow', smoothedProgress);
      progressBar.textContent = `${Math.round(smoothedProgress)}%`;
      lastProgress = smoothedProgress;
    }
  }

  // Throttled progress update function
  function throttledProgressUpdate(e) {
    if (updateTimeout) {
      clearTimeout(updateTimeout);
    }

    updateTimeout = setTimeout(() => {
      if (e.lengthComputable) {
        const percent = Math.min(99, Math.round((e.loaded / e.total) * 100));
        updateProgressBar(percent);
      }
    }, 100); // Throttle to 100ms
  }

  uploadForm.addEventListener('submit', (event) => {
    event.preventDefault();
    submitBtn.disabled = true;
    loadingSpinner.classList.remove('d-none');
    progressContainer.classList.remove('d-none');
    progressHistory.length = 0; // Clear history
    lastProgress = 0;

    const formData = new FormData(uploadForm);
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', throttledProgressUpdate);

    xhr.addEventListener('load', () => {
      // Set progress to 100% on successful completion
      updateProgressBar(100);

      if (xhr.status === 200) {
        window.location.href = xhr.responseURL || '/voicemails';
      } else {
        submitBtn.disabled = false;
        loadingSpinner.classList.add('d-none');
        progressContainer.classList.add('d-none');
        alert('Upload failed. Please try again.');
      }
    });

    xhr.addEventListener('error', () => {
      submitBtn.disabled = false;
      loadingSpinner.classList.add('d-none');
      progressContainer.classList.add('d-none');
      alert('Upload failed. Please try again.');
    });

    xhr.open('POST', uploadForm.action);
    xhr.send(formData);
  });
</script>

{% endblock %}