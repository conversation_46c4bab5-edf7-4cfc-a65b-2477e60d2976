from database import db
from flask_login import UserMixin
from datetime import datetime

class User(UserMixin, db.Model):
    __tablename__ = 'user'  # Explicitly set table name due to 'user' being reserved
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)

class Archive(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    stored_filename = db.Column(db.String(255), unique=True, nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    file_size = db.Column(db.Integer)  # Size in bytes
    processed = db.Column(db.Boolean, default=False)
    last_processed = db.Column(db.DateTime)
    voicemail_messages = db.relationship('VoicemailMessage', backref='archive', lazy=True)

    def __repr__(self):
        return f'<Archive {self.filename}>'

class VoicemailMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    mbox_id = db.Column(db.Integer)
    message_id = db.Column(db.Integer)
    ms_flags = db.Column(db.String(256))
    ms_num = db.Column(db.Integer, unique=True)
    ms_frames = db.Column(db.Integer)
    ms_num2 = db.Column(db.Integer)
    ms_frames2 = db.Column(db.Integer)
    ms_datetime = db.Column(db.DateTime)
    ms_datetime2 = db.Column(db.DateTime)
    ms_node = db.Column(db.String(256))
    ms_net_cpy_lst_idx = db.Column(db.Integer)
    ms_line_index = db.Column(db.Integer)
    ms_next = db.Column(db.Integer)
    ms_prev = db.Column(db.Integer)
    ms_snext = db.Column(db.Integer)
    ms_sprev = db.Column(db.Integer)
    ms_sender = db.Column(db.String(256))
    ms_advum_replicated = db.Column(db.Boolean)
    amis_phone = db.Column(db.String(256))
    amis_mbox = db.Column(db.String(256))
    ms_name = db.Column(db.String(256))
    ms_privacy = db.Column(db.Boolean)
    file_path = db.Column(db.String(512))
    archive_id = db.Column(db.Integer, db.ForeignKey('archive.id'), nullable=True)

    def __repr__(self):
        return f'<VoicemailMessage {self.ms_num}>'