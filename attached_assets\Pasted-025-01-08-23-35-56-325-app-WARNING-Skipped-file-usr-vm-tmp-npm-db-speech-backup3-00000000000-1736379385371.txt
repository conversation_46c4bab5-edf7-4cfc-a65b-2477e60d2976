025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000697.name.33326: [<PERSON>rrno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000999.hdr.up.003: [Errno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000699.greet.0.32961: [<PERSON>rrno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000621.greet.0.32772: [<PERSON><PERSON><PERSON> 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000699.hdr.up.005: [Errno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000622.greet.0.32752: [Errno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000162.greet.0.33071: [Errno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - WARNING - Skipped file /usr/vm/tmp/npm_db//speech_backup3/000000000000699.hdr.up.003: [Errno 30] Read-only file system: '/usr/vm'
2025-01-08 23:35:56,325 - app - ERROR - Error in process_archive: Required postgresql.txt file not found at: /tmp/tmpg5dwb03i/inner_extract/usr/vm/tmp/npm_db/postgresql.txt
Traceback (most recent call last):
  File "/home/<USER>/workspace/utils.py", line 171, in process_archive
    raise ValueError(f"Required postgresql.txt file not found at: {postgresql_path}")
ValueError: Required postgresql.txt file not found at: /tmp/tmpg5dwb03i/inner_extract/usr/vm/tmp/npm_db/postgresql.txt
2025-01-08 23:35:56,398 - app - INFO - Cleaning up temporary directory: /tmp/tmpg5dwb03i
2025-01-08 23:35:56,443 - app - INFO - Temporary directory cleaned up successfully
2025-01-08 23:35:56,443 - app - ERROR - Error reprocessing archive: Required postgresql.txt file not found at: /tmp/tmpg5dwb03i/inner_extract/usr/vm/tmp/npm_db/postgresql.txt
Traceback (most recent call last):
  File "/home/<USER>/workspace/app.py", line 197, in reprocess_archive
    messages = process_archive(f, app.config["UPLOAD_FOLDER"], archive.id)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/utils.py", line 171, in process_archive
    raise ValueError(f"Required postgresql.txt file not found at: {postgresql_path}")
ValueError: Required postgresql.txt file not found at: /tmp/tmpg5dwb03i/inner_extract/usr/vm/tmp/npm_db/postgresql.txt
2025-01-08 23:35:56,445 - werkzeug - INFO - 172.31.196.50 - - [08/Jan/2025 23:35:56] "POST /archives/1/reprocess HTTP/1.1" 302 -
2025-01-08 23:35:56,810 - app - INFO - Retrieved archives list
2025-01-08 23:35:56,882 - werkzeug - INFO - 172.31.196.50 - - [08/Jan/2025 23:35:56] "GET /archives HTTP/1.1" 200 -
2025-01-08 23:35:56,978 - werkzeug - INFO - 172.31.196.50 - - [08/Jan/2025 23:35:56] "GET /static/js/main.js HTTP/1.1" 304 