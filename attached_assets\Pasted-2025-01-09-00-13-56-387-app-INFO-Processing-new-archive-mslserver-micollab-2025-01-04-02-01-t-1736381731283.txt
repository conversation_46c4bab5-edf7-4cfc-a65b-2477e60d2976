2025-01-09 00:13:56,387 - app - INFO - Processing new archive: mslserver_micollab_2025-01-04_02-01.tgz (stored as: 5931704f1fbe4f80b900a0cca987c00f.tgz)
2025-01-09 00:13:56,516 - app - INFO - Saved archive file to: /tmp/voicemail_archives/5931704f1fbe4f80b900a0cca987c00f.tgz
2025-01-09 00:13:56,669 - app - INFO - Created archive record for: mslserver_micollab_2025-01-04_02-01.tgz
2025-01-09 00:13:56,886 - app - INFO - Starting to process archive: mslserver_micollab_2025-01-04_02-01.tgz
2025-01-09 00:13:56,887 - app - DEBUG - Created temporary directory: /tmp/archive_48j6gg31
2025-01-09 00:13:56,887 - app - <PERSON>FO - Saved uploaded file to: /tmp/archive_48j6gg31/archive.tgz
2025-01-09 00:13:56,887 - app - INFO - Archive file size: 0 bytes
2025-01-09 00:13:56,887 - app - ERROR - Error in process_archive: File appears to be invalid (size: 0 bytes)
Traceback (most recent call last):
  File "/home/<USER>/workspace/utils.py", line 104, in process_archive
    raise ValueError(f"File appears to be invalid (size: {file_size} bytes)")
ValueError: File appears to be invalid (size: 0 bytes)
2025-01-09 00:13:56,960 - app - INFO - Cleaning up temporary directory: /tmp/archive_48j6gg31
2025-01-09 00:13:56,960 - app - INFO - Temporary directory cleaned up successfully
2025-01-09 00:13:56,960 - app - ERROR - Error processing archive: File appears to be invalid (size: 0 bytes)
Traceback (most recent call last):
  File "/home/<USER>/workspace/app.py", line 161, in upload
    messages = process_archive(file, app.config["UPLOAD_FOLDER"], archive.id)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/utils.py", line 104, in process_archive
    raise ValueError(f"File appears to be invalid (size: {file_size} bytes)")
ValueError: File appears to be invalid (size: 0 bytes)
2025-01-09 00:13:56,981 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:13:56] "POST /upload HTTP/1.1" 302 -
2025-01-09 00:13:57,359 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:13:57] "GET /upload HTTP/1.1" 200 -
2025-01-09 00:13:57,750 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:13:57] "GET /upload HTTP/1.1" 200 -
2025-01-09 00:13:57,846 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:13:57] "GET /static/js/main.js HTTP/1.1" 304 -
2025-01-09 00:13:58,043 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:13:58] "POST /upload HTTP/1.1" 400 -
2025-01-09 00:14:00,704 - app - INFO - Retrieved voicemail messages list
2025-01-09 00:14:00,777 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:14:00] "GET /voicemails HTTP/1.1" 200 -
2025-01-09 00:14:00,886 - werkzeug - INFO - 172.31.196.50 - - [09/Jan/2025 00:14:00] "GET /static/js/main.js HTTP/1.1" 304 