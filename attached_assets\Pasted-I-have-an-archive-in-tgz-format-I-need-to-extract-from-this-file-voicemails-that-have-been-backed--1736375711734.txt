I have an archive in .tgz format. I need to extract from this file voicemails that have been backed up. I need an app where I can upload the tgz file, it will extract from it a tar file stored within. In that second tar file there will be a file call postgresql.txt where there is a correlation table that tells what voicemails the archive contains. Within that file there is a section that starts:

"-- Data for Name: np_mbox_message; Type: TABLE DATA; Schema: np; Owner: NuPoint" and ends with "\."

I need to parse that part of this file into a table. The table should have the headings as notied in that same section "mbox_id, message_id, ms_flags, ms_num, ms_frames, ms_num2, ms_frames2, ms_datetime, ms_datetime2, ms_node, ms_net_cpy_lst_idx, ms_line_index, ms_next, ms_prev, ms_snext, ms_sprev, ms_sender, ms_advum_replicated, amis_phone, amis_mbox, ms_name, ms_privacy".

The data for the rows is provided right after this section and is tab delimited and should be ordered in the same as the headings. Each row of data needs to be inserted into the table.

The tar archive within the tgz file that needs to be extracted is located in this folder:

\home\e-smith\npmBackup\

It will have a name similar to dbsave_micollab_20.8.1.103_20250104020007.tar and it should be the only tar archive in that folder.

(this archive name "dbsave_micollab_20.8.1.103_20250104020007.tar" may not always be exactly this).
And in these sub folders:

Within this archive the postgresql.txt file will be located at:

\usr\vm\tmp\npm_db\postgresql.txt

In the table of voicemails parsed form this file I want the 4th column to be a link to download the actual voicemail file. That column will be labeled "ms_num". I need you to find the find the files for each ms_nub listed in the table in one of these 4 folders.

 Vm recordings are in 

\usr\vm\tmp\npm_db\speech_backup
\usr\vm\tmp\npm_db\speech_backup1
\usr\vm\tmp\npm_db\speech_backup2
\usr\vm\tmp\npm_db\speech_backup3

The link to download the voicemail should link to the files location and let me download it in the browser. This app should have simple password protection or now with the username admin and password of "Tr@mVM"

Before starting please ask any clarifying questions.