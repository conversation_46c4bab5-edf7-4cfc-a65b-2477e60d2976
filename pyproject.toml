[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "email-validator>=2.2.0",
    "flask-migrate>=4.0.7",
    "flask>=3.1.0",
    "flask-login>=0.6.3",
    "flask-sqlalchemy>=3.1.1",
    "flask-wtf>=1.2.2",
    "psycopg2-binary>=2.9.10",
    "sqlalchemy>=2.0.36",
    "werkzeug>=3.1.3",
    "wtforms>=3.2.1",
    "trafilatura>=2.0.0",
]
