ProgrammingError
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column voicemail_message.archive_id does not exist
LINE 1: ...message.file_path AS voicemail_message_file_path, voicemail_...
                                                             ^

[SQL: SELECT voicemail_message.id AS voicemail_message_id, voicemail_message.mbox_id AS voicemail_message_mbox_id, voicemail_message.message_id AS voicemail_message_message_id, voicemail_message.ms_flags AS voicemail_message_ms_flags, voicemail_message.ms_num AS voicemail_message_ms_num, voicemail_message.ms_frames AS voicemail_message_ms_frames, voicemail_message.ms_num2 AS voicemail_message_ms_num2, voicemail_message.ms_frames2 AS voicemail_message_ms_frames2, voicemail_message.ms_datetime AS voicemail_message_ms_datetime, voicemail_message.ms_datetime2 AS voicemail_message_ms_datetime2, voicemail_message.ms_node AS voicemail_message_ms_node, voicemail_message.ms_net_cpy_lst_idx AS voicemail_message_ms_net_cpy_lst_idx, voicemail_message.ms_line_index AS voicemail_message_ms_line_index, voicemail_message.ms_next AS voicemail_message_ms_next, voicemail_message.ms_prev AS voicemail_message_ms_prev, voicemail_message.ms_snext AS voicemail_message_ms_snext, voicemail_message.ms_sprev AS voicemail_message_ms_sprev, voicemail_message.ms_sender AS voicemail_message_ms_sender, voicemail_message.ms_advum_replicated AS voicemail_message_ms_advum_replicated, voicemail_message.amis_phone AS voicemail_message_amis_phone, voicemail_message.amis_mbox AS voicemail_message_amis_mbox, voicemail_message.ms_name AS voicemail_message_ms_name, voicemail_message.ms_privacy AS voicemail_message_ms_privacy, voicemail_message.file_path AS voicemail_message_file_path, voicemail_message.archive_id AS voicemail_message_archive_id 
FROM voicemail_message]
(Background on this error at: https://sqlalche.me/e/20/f405)

Traceback (most recent call last)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
self.dialect.do_execute(
^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
cursor.execute(statement, parameters)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
The above exception was the direct cause of the following exception:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1536, in __call__
return self.wsgi_app(environ, start_response)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1514, in wsgi_app
response = self.handle_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 1511, in wsgi_app
response = self.full_dispatch_request()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 919, in full_dispatch_request
rv = self.handle_user_exception(e)
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 917, in full_dispatch_request
rv = self.dispatch_request()
     ^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask/app.py", line 902, in dispatch_request
return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/flask_login/utils.py", line 290, in decorated_view
return current_app.ensure_sync(func)(*args, **kwargs)
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/app.py", line 248, in voicemails
messages = VoicemailMessage.query.all()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2673, in all
return self._iter().all()  # type: ignore
       ^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/query.py", line 2827, in _iter
result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                              
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2362, in execute
return self._execute_internal(
       
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2247, in _execute_internal
result: Result[Any] = compile_state_cls.orm_execute_statement(
                      
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
result = conn.execute(
         
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1418, in execute
return meth(
       
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
return connection._execute_clauseelement(
       
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1640, in _execute_clauseelement
ret = self._execute_context(
      
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1846, in _execute_context
return self._exec_single_context(
       
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1986, in _exec_single_context
self._handle_dbapi_exception(
^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2355, in _handle_dbapi_exception
raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
self.dialect.do_execute(
^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
cursor.execute(statement, parameters)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column voicemail_message.archive_id does not exist
LINE 1: ...message.file_path AS voicemail_message_file_path, voicemail_...
^

[SQL: SELECT voicemail_message.id AS voicemail_message_id, voicemail_message.mbox_id AS voicemail_message_mbox_id, voicemail_message.message_id AS voicemail_message_message_id, voicemail_message.ms_flags AS voicemail_message_ms_flags, voicemail_message.ms_num AS voicemail_message_ms_num, voicemail_message.ms_frames AS voicemail_message_ms_frames, voicemail_message.ms_num2 AS voicemail_message_ms_num2, voicemail_message.ms_frames2 AS voicemail_message_ms_frames2, voicemail_message.ms_datetime AS voicemail_message_ms_datetime, voicemail_message.ms_datetime2 AS voicemail_message_ms_datetime2, voicemail_message.ms_node AS voicemail_message_ms_node, voicemail_message.ms_net_cpy_lst_idx AS voicemail_message_ms_net_cpy_lst_idx, voicemail_message.ms_line_index AS voicemail_message_ms_line_index, voicemail_message.ms_next AS voicemail_message_ms_next, voicemail_message.ms_prev AS voicemail_message_ms_prev, voicemail_message.ms_snext AS voicemail_message_ms_snext, voicemail_message.ms_sprev AS voicemail_message_ms_sprev, voicemail_message.ms_sender AS voicemail_message_ms_sender, voicemail_message.ms_advum_replicated AS voicemail_message_ms_advum_replicated, voicemail_message.amis_phone AS voicemail_message_amis_phone, voicemail_message.amis_mbox AS voicemail_message_amis_mbox, voicemail_message.ms_name AS voicemail_message_ms_name, voicemail_message.ms_privacy AS voicemail_message_ms_privacy, voicemail_message.file_path AS voicemail_message_file_path, voicemail_message.archive_id AS voicemail_message_archive_id
FROM voicemail_message]
(Background on this error at: https://sqlalche.me/e/20/f405)
The debugger caught an exception in your WSGI application. You can now look at the traceback which led to the error.
To switch between the interactive traceback and the plaintext one, you can click on the "Traceback" headline. From the text traceback you can also create a paste of it.