"""Initial migration

Revision ID: initial_migration
Revises: 
Create Date: 2025-01-08 23:14:38.544

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'initial_migration'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create user table with proper quoting for PostgreSQL
    op.execute('DROP TABLE IF EXISTS "user" CASCADE')
    op.create_table('user',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=64), nullable=False),
        sa.Column('password_hash', sa.String(length=256), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username')
    )

    # Create archive table
    op.execute('DROP TABLE IF EXISTS archive CASCADE')
    op.create_table('archive',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('filename', sa.String(length=255), nullable=False),
        sa.Column('stored_filename', sa.String(length=255), nullable=False),
        sa.Column('upload_date', sa.DateTime(), nullable=True),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('processed', sa.Boolean(), default=False),
        sa.Column('last_processed', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('stored_filename')
    )

    # Create voicemail_message table
    op.execute('DROP TABLE IF EXISTS voicemail_message CASCADE')
    op.create_table('voicemail_message',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('mbox_id', sa.Integer(), nullable=True),
        sa.Column('message_id', sa.Integer(), nullable=True),
        sa.Column('ms_flags', sa.String(length=256), nullable=True),
        sa.Column('ms_num', sa.Integer(), nullable=True),
        sa.Column('ms_frames', sa.Integer(), nullable=True),
        sa.Column('ms_num2', sa.Integer(), nullable=True),
        sa.Column('ms_frames2', sa.Integer(), nullable=True),
        sa.Column('ms_datetime', sa.DateTime(), nullable=True),
        sa.Column('ms_datetime2', sa.DateTime(), nullable=True),
        sa.Column('ms_node', sa.String(length=256), nullable=True),
        sa.Column('ms_net_cpy_lst_idx', sa.Integer(), nullable=True),
        sa.Column('ms_line_index', sa.Integer(), nullable=True),
        sa.Column('ms_next', sa.Integer(), nullable=True),
        sa.Column('ms_prev', sa.Integer(), nullable=True),
        sa.Column('ms_snext', sa.Integer(), nullable=True),
        sa.Column('ms_sprev', sa.Integer(), nullable=True),
        sa.Column('ms_sender', sa.String(length=256), nullable=True),
        sa.Column('ms_advum_replicated', sa.Boolean(), nullable=True),
        sa.Column('amis_phone', sa.String(length=256), nullable=True),
        sa.Column('amis_mbox', sa.String(length=256), nullable=True),
        sa.Column('ms_name', sa.String(length=256), nullable=True),
        sa.Column('ms_privacy', sa.Boolean(), nullable=True),
        sa.Column('file_path', sa.String(length=512), nullable=True),
        sa.Column('archive_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['archive_id'], ['archive.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('voicemail_message')
    op.drop_table('archive')
    op.drop_table('user')