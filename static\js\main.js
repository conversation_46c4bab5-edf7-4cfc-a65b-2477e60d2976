// Enable Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // File upload handling
    var uploadForm = document.getElementById('uploadForm');
    var fileInput = document.querySelector('input[type="file"]');
    var submitBtn = document.getElementById('submitBtn');
    var loadingSpinner = document.getElementById('loadingSpinner');
    var progressContainer = document.getElementById('progressContainer');
    var progressBar = progressContainer?.querySelector('.progress-bar');

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            var fileName = e.target.files[0].name;
            if (!fileName.endsWith('.tgz')) {
                alert('Please select a .tgz file');
                e.target.value = '';
            }
        });
    }

    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();

            var formData = new FormData(uploadForm);
            var xhr = new XMLHttpRequest();

            // Show progress container
            progressContainer.classList.remove('d-none');

            // Disable submit button and show spinner
            submitBtn.disabled = true;
            loadingSpinner.classList.remove('d-none');
            submitBtn.querySelector('.me-2').textContent = 'Uploading...';

            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    var percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                    progressBar.setAttribute('aria-valuenow', percentComplete);
                    progressBar.textContent = Math.round(percentComplete) + '%';
                }
            });

            xhr.addEventListener('load', function(e) {
                if (xhr.status === 200) {
                    window.location.href = xhr.responseURL || '/voicemails';
                } else {
                    // Enable submit button and hide spinner
                    submitBtn.disabled = false;
                    loadingSpinner.classList.add('d-none');
                    submitBtn.querySelector('.me-2').textContent = 'Upload and Process';
                    progressContainer.classList.add('d-none');

                    // Show error message
                    alert('Upload failed. Please try again.');
                }
            });

            xhr.addEventListener('error', function(e) {
                // Enable submit button and hide spinner
                submitBtn.disabled = false;
                loadingSpinner.classList.add('d-none');
                submitBtn.querySelector('.me-2').textContent = 'Upload and Process';
                progressContainer.classList.add('d-none');

                // Show error message
                alert('Upload failed. Please try again.');
            });

            xhr.open('POST', uploadForm.action, true);
            xhr.send(formData);
        });
    }
});