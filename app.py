import os
import logging
from datetime import datetime
from flask import Flask, flash, redirect, render_template, request, url_for, send_file, after_this_request
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, login_required, logout_user, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from flask_wtf.csrf import CSR<PERSON>rotect, CSRFError
from flask_wtf import <PERSON>laskForm
from wtforms import String<PERSON>ield, PasswordField
from wtforms.validators import DataRequired
import uuid
from flask_migrate import Migrate
from database import db
from models import User, Archive, VoicemailMessage
from utils import convert_raw_to_mp3, process_archive, allowed_file, cleanup_temp_files

# Configure logging first, before any other operations
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LoginForm(FlaskForm):
    username = String<PERSON>ield('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])

# create the app
app = Flask(__name__)

app.config['SECRET_KEY'] = os.urandom(24)  # Generate a new secret key on startup
app.config["SQLALCHEMY_DATABASE_URI"] = os.environ.get("DATABASE_URL")
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_recycle": 300,
    "pool_pre_ping": True,
}
app.config["UPLOAD_FOLDER"] = "/tmp/voicemail_uploads"
app.config["ARCHIVE_FOLDER"] = "/tmp/voicemail_archives"
app.config["MAX_CONTENT_LENGTH"] = 500 * 1024 * 1024  # 500MB limit
app.config['WTF_CSRF_ENABLED'] = True
app.config['WTF_CSRF_SECRET_KEY'] = os.urandom(24)  # Separate CSRF secret key

csrf = CSRFProtect()
csrf.init_app(app)

db.init_app(app)
migrate = Migrate(app, db)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = "login"

# Ensure upload directories exist and are writable
for folder in [app.config["UPLOAD_FOLDER"], app.config["ARCHIVE_FOLDER"]]:
    if not os.path.exists(folder):
        try:
            os.makedirs(folder, mode=0o755, exist_ok=True)
            logger.info(f"Created directory: {folder}")
        except Exception as e:
            logger.error(f"Failed to create directory {folder}: {e}")


@app.errorhandler(CSRFError)
def handle_csrf_error(e):
    logger.warning("CSRF validation failed")
    flash("The form session has expired. Please try again.", "error")
    return redirect(url_for('login')), 400

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

@app.route("/login", methods=["GET", "POST"])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))

    form = LoginForm()
    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data
        logger.debug(f"Login attempt for username: {username}")

        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            logger.info(f"User {username} logged in successfully")

            next_page = request.args.get('next')
            if next_page:
                logger.info(f"Redirecting to requested page: {next_page}")
                return redirect(next_page)

            logger.info("No next page specified, redirecting to upload page")
            return redirect(url_for("upload"))

        flash("Invalid credentials", "error")
        logger.warning(f"Failed login attempt for username: {username}")

    return render_template("login.html", form=form)

@app.route("/logout")
@login_required
def logout():
    logout_user()
    logger.info("User logged out")
    return redirect(url_for("login"))

@app.route("/")
@login_required
def index():
    return redirect(url_for("upload"))

@app.route("/upload", methods=["GET", "POST"])
@login_required
def upload():
    if request.method == "POST":
        if "file" not in request.files:
            flash("No file uploaded", "error")
            logger.warning("Upload attempted with no file")
            return redirect(request.url)

        file = request.files["file"]
        if file.filename == "":
            flash("No file selected", "error")
            logger.warning("Upload attempted with empty filename")
            return redirect(request.url)

        if not allowed_file(file.filename):
            flash("Invalid file type. Please upload a .tgz file", "error")
            logger.warning(f"Invalid file type attempted: {file.filename}")
            return redirect(request.url)

        try:
            # Generate unique filename for storage
            stored_filename = f"{uuid.uuid4().hex}{os.path.splitext(file.filename)[1]}"
            archive_path = os.path.join(app.config["ARCHIVE_FOLDER"], stored_filename)
            logger.info(f"Processing new archive: {file.filename} (stored as: {stored_filename})")

            # Save the archive file first
            file.save(archive_path)
            logger.info(f"Saved archive file to: {archive_path}")

            # Verify file was saved and has content
            if not os.path.exists(archive_path):
                raise ValueError("Failed to save archive file")

            file_size = os.path.getsize(archive_path)
            if file_size == 0:
                raise ValueError("Uploaded file is empty")

            logger.info(f"Verified archive file size: {file_size} bytes")

            # Create archive record
            archive = Archive(
                filename=file.filename,
                stored_filename=stored_filename,
                file_size=file_size
            )
            db.session.add(archive)
            db.session.commit()
            logger.info(f"Created archive record for: {file.filename}")

            # Process the archive by opening the saved file
            with open(archive_path, 'rb') as f:
                messages = process_archive(f, app.config["UPLOAD_FOLDER"], archive.id)

            # Update archive record
            archive.processed = True
            archive.last_processed = datetime.utcnow()
            db.session.commit()
            logger.info(f"Successfully processed archive: {file.filename}")

            flash(f"Successfully processed {len(messages)} voicemail records", "success")
            return redirect(url_for("voicemails"))
        except Exception as e:
            logger.error(f"Error processing archive: {str(e)}", exc_info=True)
            flash(f"Error processing archive: {str(e)}", "error")
            return redirect(url_for("upload"))

    return render_template("upload.html")

@app.route("/archives")
@login_required
def list_archives():
    archives = Archive.query.order_by(Archive.upload_date.desc()).all()
    logger.info("Retrieved archives list")
    return render_template("archives.html", archives=archives)

@app.route("/archives/<int:archive_id>/reprocess", methods=["POST"])
@login_required
def reprocess_archive(archive_id):
    archive = Archive.query.get_or_404(archive_id)
    try:
        logger.info(f"Starting reprocess of archive: {archive.filename}")
        archive_path = os.path.join(app.config["ARCHIVE_FOLDER"], archive.stored_filename)
        with open(archive_path, 'rb') as f:
            messages = process_archive(f, app.config["UPLOAD_FOLDER"], archive.id)

        archive.processed = True
        archive.last_processed = datetime.utcnow()
        db.session.commit()

        flash(f"Successfully reprocessed {len(messages)} voicemail records", "success")
        logger.info(f"Successfully reprocessed archive: {archive.filename}")
    except Exception as e:
        logger.error(f"Error reprocessing archive: {str(e)}", exc_info=True)
        flash(f"Error reprocessing archive: {str(e)}", "error")

    return redirect(url_for("list_archives"))

@app.route("/archives/<int:archive_id>/delete", methods=["POST"])
@login_required
def delete_archive(archive_id):
    archive = Archive.query.get_or_404(archive_id)
    try:
        logger.info(f"Attempting to delete archive: {archive.filename}")
        # Delete the physical file
        archive_path = os.path.join(app.config["ARCHIVE_FOLDER"], archive.stored_filename)
        if os.path.exists(archive_path):
            os.remove(archive_path)
            logger.info(f"Deleted archive file: {archive_path}")

        # Delete associated voicemail messages
        VoicemailMessage.query.filter_by(archive_id=archive.id).delete()
        logger.info(f"Deleted voicemail messages for archive: {archive.filename}")

        # Delete the archive record
        db.session.delete(archive)
        db.session.commit()
        logger.info(f"Deleted archive record: {archive.filename}")

        flash("Archive deleted successfully", "success")
    except Exception as e:
        logger.error(f"Error deleting archive: {str(e)}", exc_info=True)
        flash(f"Error deleting archive: {str(e)}", "error")

    return redirect(url_for("list_archives"))

@app.route("/voicemails")
@login_required
def voicemails():
    messages = VoicemailMessage.query.all()
    logger.info("Retrieved voicemail messages list")
    return render_template("voicemails.html", messages=messages)


@app.route("/download/<int:ms_num>")
@login_required
def download_voicemail(ms_num):
    message = VoicemailMessage.query.filter_by(ms_num=ms_num).first_or_404()
    if not message.file_path or not os.path.exists(message.file_path):
        logger.error(f"Voicemail file not found for ms_num: {ms_num}")
        flash("Voicemail file not found", "error")
        return redirect(url_for("voicemails"))

    try:
        logger.info(f"Converting voicemail file for ms_num: {ms_num}")
        mp3_path = convert_raw_to_mp3(message.file_path)

        @after_this_request
        def cleanup(response):
            try:
                if os.path.exists(mp3_path):
                    os.remove(mp3_path)
                    logger.info(f"Cleaned up temporary MP3 file: {mp3_path}")
            except Exception as e:
                logger.error(f"Error cleaning up MP3 file: {str(e)}")
            return response

        logger.info(f"Sending converted MP3 file for ms_num: {ms_num}")
        return send_file(
            mp3_path,
            mimetype="audio/mpeg",
            as_attachment=True,
            download_name=f"voicemail_{ms_num}.mp3"
        )
    except Exception as e:
        logger.error(f"Error processing voicemail download: {str(e)}", exc_info=True)
        flash(f"Error processing voicemail: {str(e)}", "error")
        return redirect(url_for("voicemails"))

@app.route("/clear_voicemails", methods=["POST"])
@login_required
def clear_voicemails():
    try:
        # Get all voicemail messages to clean up their files
        messages = VoicemailMessage.query.all()
        for message in messages:
            if message.file_path and os.path.exists(message.file_path):
                try:
                    os.remove(message.file_path)
                    logger.info(f"Deleted voicemail file: {message.file_path}")
                except Exception as e:
                    logger.error(f"Error deleting voicemail file {message.file_path}: {str(e)}")

        # Delete all voicemail records
        VoicemailMessage.query.delete()
        db.session.commit()
        logger.info("Cleared all voicemail messages from database")

        flash("Successfully cleared all voicemail messages", "success")
    except Exception as e:
        logger.error(f"Error clearing voicemail messages: {str(e)}", exc_info=True)
        db.session.rollback()
        flash(f"Error clearing voicemail messages: {str(e)}", "error")

    return redirect(url_for("voicemails"))

with app.app_context():
    # Create tables if they don't exist, but don't drop existing ones
    logger.info("Ensuring database tables exist")
    db.create_all()
    logger.info("Database tables initialized")