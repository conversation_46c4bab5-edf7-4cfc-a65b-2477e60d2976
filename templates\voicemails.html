{% extends "base.html" %}

{% block content %}
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="card-title mb-0">Search Filters</h4>
            <form action="{{ url_for('clear_voicemails') }}" method="POST" onsubmit="return confirm('Are you sure you want to clear all voicemail records? This action cannot be undone.');">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <button type="submit" class="btn btn-danger">Clear All Voicemails</button>
            </form>
        </div>
        <form id="searchForm" class="row g-3">
            <div class="col-md-2">
                <label for="idSearch" class="form-label">Message Number</label>
                <input type="text" class="form-control" id="idSearch" placeholder="Search by ms_num">
            </div>
            <div class="col-md-3">
                <label for="senderSearch" class="form-label">Sender</label>
                <input type="text" class="form-control" id="senderSearch" placeholder="Search by sender">
            </div>
            <div class="col-md-2">
                <label for="dateStart" class="form-label">Start Date</label>
                <input type="datetime-local" class="form-control" id="dateStart">
            </div>
            <div class="col-md-2">
                <label for="dateEnd" class="form-label">End Date</label>
                <input type="datetime-local" class="form-control" id="dateEnd">
            </div>
            <div class="col-md-3">
                <label for="timezone" class="form-label">Timezone</label>
                <select class="form-select" id="timezone">
                    <option value="America/Los_Angeles">Pacific Time (PST/PDT)</option>
                    <option value="America/Denver">Mountain Time (MST/MDT)</option>
                    <option value="America/Chicago">Central Time (CST/CDT)</option>
                    <option value="America/New_York">Eastern Time (EST/EDT)</option>
                    <option value="UTC">UTC</option>
                </select>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <h2 class="card-title">Voicemail Messages</h2>
        <div class="table-responsive">
            <table id="voicemailTable" class="table table-striped">
                <thead>
                    <tr>
                        <th>Mailbox ID</th>
                        <th>Message #</th>
                        <th>Date Created</th>
                        <th>Sender</th>
                        <th>Name</th>
                        <th>Download</th>
                    </tr>
                </thead>
                <tbody>
                    {% for message in messages %}
                    <tr>
                        <td>{{ message.mbox_id or 'N/A' }}</td>
                        <td>{{ message.ms_num }}</td>
                        <td data-sort="{{ message.ms_datetime.isoformat() if message.ms_datetime else '' }}" 
                            data-utc="{{ message.ms_datetime.isoformat() if message.ms_datetime else '' }}">
                            {{ message.ms_datetime.strftime('%Y-%m-%d %H:%M:%S') if message.ms_datetime else 'N/A' }}
                        </td>
                        <td>{{ message.ms_sender or 'Unknown' }}</td>
                        <td>{{ message.ms_name or 'N/A' }}</td>
                        <td>
                            {% if message.file_path %}
                            <a href="{{ url_for('download_voicemail', ms_num=message.ms_num) }}" 
                               class="btn btn-sm btn-primary">
                                Download
                            </a>
                            {% else %}
                            <span class="badge bg-warning">Not Found</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Set default timezone to PST
    moment.tz.setDefault('America/Los_Angeles');

    // Function to update displayed dates based on selected timezone
    function updateDates() {
        var selectedTz = $('#timezone').val();
        $('#voicemailTable tbody tr').each(function() {
            var dateCell = $(this).find('td:eq(2)');
            var utcDate = dateCell.attr('data-utc');
            if (utcDate) {
                var localDate = moment.utc(utcDate).tz(selectedTz).format('YYYY-MM-DD HH:mm:ss');
                dateCell.text(localDate);
            }
        });
    }

    // Initialize DataTable
    var table = $('#voicemailTable').DataTable({
        order: [[2, 'desc']], // Sort by date created by default
        pageLength: 25,
        language: {
            search: "Quick Search:"
        }
    });

    // Custom filtering function
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
        var idFilter = $('#idSearch').val().toLowerCase();
        var senderFilter = $('#senderSearch').val().toLowerCase();
        var startDate = $('#dateStart').val() ? moment.tz($('#dateStart').val(), $('#timezone').val()) : null;
        var endDate = $('#dateEnd').val() ? moment.tz($('#dateEnd').val(), $('#timezone').val()) : null;

        var messageNum = data[1].toLowerCase();  // ms_num is in column 1
        var sender = data[3].toLowerCase();      // sender is in column 3
        var dateCell = $(table.cell(dataIndex, 2).node());  // date created is in column 2
        var date = dateCell.attr('data-utc') ? moment.utc(dateCell.attr('data-utc')) : null;

        // Message number filter
        if (idFilter && !messageNum.includes(idFilter)) {
            return false;
        }

        // Sender filter
        if (senderFilter && !sender.includes(senderFilter)) {
            return false;
        }

        // Date range filter
        if (date) {
            if (startDate && date.isBefore(startDate)) return false;
            if (endDate && date.isAfter(endDate)) return false;
        }

        return true;
    });

    // Trigger search on filter changes
    $('#idSearch, #senderSearch, #dateStart, #dateEnd').on('change keyup', function() {
        table.draw();
    });

    // Update dates when timezone changes
    $('#timezone').on('change', function() {
        updateDates();
        table.draw();
    });

    // Initial date display update
    updateDates();
});
</script>
{% endblock %}