2025-01-08 23:33:14,386 - app - DEBUG - Extracting: root/.rpmmacros
2025-01-08 23:33:14,386 - app - ERROR - Error in process_archive: [Errno 13] Permission denied: '/tmp/tmprht2f8_d/root/.rpmmacros'
Traceback (most recent call last):
  File "/home/<USER>/workspace/utils.py", line 121, in process_archive
    tgz.extract(member, temp_dir)
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python3.11/tarfile.py", line 2327, in extract
    self._extract_one(tarinfo, path, set_attrs, numeric_owner)
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python3.11/tarfile.py", line 2361, in _extract_one
    self._handle_fatal_error(e)
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python3.11/tarfile.py", line 2357, in _extract_one
    self._extract_member(tarinfo, os.path.join(path, tarinfo.name),
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python3.11/tarfile.py", line 2440, in _extract_member
    self.makefile(tarinfo, targetpath)
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python3.11/tarfile.py", line 2486, in makefile
    with bltn_open(targetpath, "wb") as target:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: '/tmp/tmprht2f8_d/root/.rpmmacros'
2025-01-08 23:33:14,461 - app - INFO - Cleaning up temporary directory: /tmp/tmprht2f8_d
2025-01-08 23:33:14,571 - app - INFO - Temporary directory cleaned up successfully
2025-01-08 23:33:14,571 - app - ERROR - Error reprocessing archive: [Errno 13] Permission denied: '/tmp/tmprht2f8_d/root/.rpmmacros'
Traceback (most recent call last):
  File "/home/<USER>/workspace/app.py", line 197, in reprocess_archive
    messages = process_archive(f, app.config["UPLOAD_FOLDER"], archive.id)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/workspace/utils.py", line 121, in process_archive
    tgz.extract(member, temp_dir)
  File "/nix/store/clx0mcir7qw8zk36zbr4jra789g3knf6-python3-3.11.10/lib/python