{% extends "base.html" %}

{% block content %}
<div class="card">
    <div class="card-body">
        <h2 class="card-title">Uploaded Archives</h2>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Filename</th>
                        <th>Upload Date</th>
                        <th>Size</th>
                        <th>Last Processed</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for archive in archives %}
                    <tr>
                        <td>{{ archive.filename }}</td>
                        <td>{{ archive.upload_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>{{ (archive.file_size / 1024 / 1024) | round(2) }} MB</td>
                        <td>
                            {% if archive.last_processed %}
                            {{ archive.last_processed.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% else %}
                            Never
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <form method="POST" action="{{ url_for('reprocess_archive', archive_id=archive.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-primary me-2">Reprocess</button>
                                </form>
                                <form method="POST" action="{{ url_for('delete_archive', archive_id=archive.id) }}" 
                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this archive?');">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
